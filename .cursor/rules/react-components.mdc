---
description: React Component Development Guidelines
globs: ["apps/web/src/components/**/*.tsx", "apps/web/src/app/**/*.tsx"]
alwaysApply: false
---

# React Component Standards

## Component Patterns
- Use functional components with hooks only
- Custom hooks for reusable logic
- Proper dependency arrays in useEffect
- No missing keys in lists
- No components defined inside other components
- Use `<>...</>` instead of `<React.Fragment>`

## Component Structure
```typescript
// Props interface above component
interface UserProfileProps {
  userId: string;
  onUpdate?: (user: User) => void;
}

// Default export functional component
export default function UserProfile({ userId, onUpdate }: UserProfileProps) {
  // Component logic here
  return (
    <div>
      {/* JSX here */}
    </div>
  );
}
```

## Import Organization
1. External libraries (React, Next.js, etc.)
2. Internal utilities and components
3. Relative imports
4. Type-only imports (use `import type`)

## shadcn/ui Integration
- Use existing shadcn/ui components from `src/components/ui/`
- Follow the established component patterns
- Extend components using the `cn()` utility for className merging

## Accessibility Requirements
- Semantic HTML elements preferred
- Proper ARIA attributes when needed
- Include `type` attribute for button elements
- Pair onClick with keyboard handlers
- Screen reader compatible content
- No positive tabIndex values

## Error Handling
- Comprehensive error boundaries
- Loading and error states for async operations
- Graceful fallbacks for missing data

## Current Component State (January 8, 2025)
- **Layout System**: Root layout, header, sidebar, providers with comprehensive error boundaries
- **Error Handling**: Error boundaries, error display, global error pages with recovery and retry mechanisms
- **Loading States**: Multiple loading variants (skeleton, inline, page, button loaders) with accessibility support
- **UI Components**: Complete shadcn/ui integration (20+ components: Button, Card, Input, Switch, Sidebar, Skeleton, etc.)
- **Navigation**: App sidebar with collapsible navigation, breadcrumb system, and keyboard navigation
- **Theme System**: Dark/light mode with system preference detection, persistence, and smooth transitions
- **Authentication**: Mock authentication context with localStorage persistence for development (Clerk integration planned)
- **API Integration**: Type-safe API client with comprehensive error handling, retry logic, and network error detection
- **Accessibility**: WCAG 2.1 AA compliance with ARIA labels, keyboard navigation, screen reader support, and focus management
- **Marketing Page**: Norwegian content with VSME Guru branding, responsive design, and accessibility features

## Established Patterns (Required)

### Error Handling Pattern
```typescript
import { ErrorBoundary } from '@/components/common/error-boundary'

// Always wrap components
<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>

// API calls with error handling
const { data, error, isLoading, execute } = useApiCall(apiFunction)
if (error) return <ErrorDisplay error={error} onRetry={execute} />
```

### Loading State Pattern
```typescript
import { InlineLoader, PageLoader, ButtonLoader } from '@/components/common/loading'

// Button loading
<Button disabled={isLoading}>
  {isLoading ? <ButtonLoader className="mr-2" /> : null}
  Submit
</Button>
```

### Accessibility Pattern
```typescript
// Navigation with proper ARIA roles
<SidebarMenu role="navigation" aria-label="Main navigation">
  <Link 
    href={item.url}
    aria-expanded={item.isActive}
    aria-describedby={item.items?.length ? `${item.title}-submenu` : undefined}
  >
    {item.icon && <item.icon aria-hidden="true" />}
    <span>{item.title}</span>
  </Link>
</SidebarMenu>

// User menu with descriptive labels
<SidebarMenuButton aria-label={`User menu for ${user.name}`}>
  <Avatar>
    <AvatarImage alt={`${user.name}'s profile picture`} src={user.avatar} />
    <AvatarFallback aria-label={`${user.name} initials`}>
      {initials}
    </AvatarFallback>
  </Avatar>
</SidebarMenuButton>
```

## shadcn/ui Integration
When building UI components:
1. Use `list_components()` and `list_blocks()` to discover available assets
2. Prioritize blocks for complex patterns (dashboards, login forms)
3. Call `get_component_demo()` before using any component
4. Use `get_component()` for single components, `get_block()` for composite blocks

Always examine existing components in `apps/web/src/components/` before creating new ones.