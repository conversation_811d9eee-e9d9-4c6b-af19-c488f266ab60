---
description: VSME Guru SaaS Platform - Project Context and Development Standards
globs: ["**/*"]
alwaysApply: true
---

# VSME Guru Development Standards

## Project Context
**VSME Guru** is a sustainability reporting platform for the SME market, conformant to the VSME EU standard and NSRS. Production-ready error handling and loading states implemented. Complete UI foundation with responsive sidebar navigation, comprehensive accessibility features, and theme system. Mock authentication system for development. Backend ready for database models and API route expansion.

## Technology Stack
- **Runtime**: Bun v1.2.19+ (primary), Node.js (compatible)
- **Frontend**: Next.js 15.3.0, React 19, TailwindCSS 4.1.11, shadcn/ui
- **Backend**: Hono 4.8.10, Prisma 6.13.0, MongoDB, Zod validation
- **Quality**: Biome linter/formatter with Ultracite config
- **Build**: Turborepo monorepo with caching

## Key Commands
```bash
bun dev              # Start all apps
bun dev:web          # Frontend (port 3001)
bun dev:server       # Backend (port 3000) 
bun check            # Biome lint/format
bun check-types      # TypeScript check
bun db:push          # Push schema changes
```

## Code Standards

### TypeScript Rules
- Strict mode enforced, zero `any` types
- Use `import type` for types, `export type` for exports
- No TypeScript enums, namespaces, or non-null assertions
- Prefer `T[]` over `Array<T>`, use `as const` for literals

### React/Next.js
- Functional components with hooks only
- No components inside components, proper hook dependencies
- Use `<>...</>` instead of `React.Fragment`
- Next.js Image component for images, no `<img>` tags

### Accessibility (Required)
- Semantic HTML, proper ARIA attributes
- No positive tabIndex values, include `type` on buttons
- Pair onClick with onKeyUp/onKeyDown/onKeyPress
- Screen reader compatible, keyboard navigable

### File Structure
```
apps/
├── server/          # Hono API routes
└── web/             # Next.js components
```

### Naming Conventions
- Files: `kebab-case` (user-profile.tsx)
- Components: `PascalCase` (UserProfile)
- Functions: `camelCase` (getUserProfile)
- Constants: `SCREAMING_SNAKE_CASE`

### Security & Performance
- No hardcoded secrets, environment variables only
- Input validation with Zod, comprehensive error handling
- Next.js Image component, loading states, React.memo for expensive components

## Current Implementation Status (January 8, 2025)
Following SaaS UI Foundation spec - Major accomplishments completed:
- [x] **Backend API foundation** with Hono server structure and health check route
- [x] **Database and validation setup** with Prisma and Zod (schema ready for models)
- [x] **Mock authentication context** for UI development with localStorage persistence
- [x] **Complete sidebar-07 block implementation** with collapsible navigation
- [x] **Comprehensive error handling system** (boundaries, global pages, recovery, retry logic)
- [x] **Loading states system** (skeleton, inline, page, button loaders with accessibility)
- [x] **Marketing page** with VSME Guru branding and Norwegian content
- [x] **Dashboard layout** with responsive sidebar and breadcrumb navigation
- [x] **Complete shadcn/ui component integration** with accessibility features (20+ components)
- [x] **API client** with error handling, retry logic, and type safety
- [x] **Accessibility implementation** (WCAG 2.1 AA compliance, ARIA labels, keyboard navigation, screen reader support)
- [x] **Focus management system** for route changes and keyboard navigation
- [x] **Theme system** with dark/light mode and system preference detection
- [x] **Code quality** with Biome + Ultracite (no semicolons preference)
- [ ] **Real authentication with Clerk** (planned - comprehensive spec documented)
- [ ] **Database connection and first models** (ready to implement - schema configured)
- [ ] **API routes expansion** (ready to implement - Hono structure ready)
- [ ] **Testing framework setup** (Vitest, Testing Library, Playwright planned with strategy documented)

## AI Agent Guidelines
- **Never start dev servers** with executeBash (hangs execution)
- Ask user to start servers, use `curl` for API testing
- Check current state documents before implementing features
- Use shadcn/ui MCP tools: `list_components()`, `get_block()`, `get_component_demo()`
- Update documentation after implementation, not before

## Key Insights from Implementation
1. **Error Boundaries Are Critical**: Implementing comprehensive error handling early prevents cascading failures and improves debugging
2. **Loading States Matter**: Consistent loading experience significantly improves perceived performance and user confidence
3. **Code Quality Tools**: Biome + Ultracite combination provides excellent developer experience with subsecond performance
4. **Documentation Evolution**: Update docs after implementation, not before - use real code examples from actual implementation
5. **Accessibility First**: WCAG 2.1 AA compliance, ARIA labels, keyboard navigation, and screen reader support implemented from the start
6. **Focus Management**: Automatic focus management on route changes improves accessibility and UX significantly
7. **Semicolon Preference**: Team preference for no semicolons successfully implemented with "asNeeded" configuration
8. **Type Safety First**: TypeScript-first approach prevented many runtime issues and improved developer confidence

## Established Best Practices
1. **Always wrap components with error boundaries**
2. **Implement loading states for all async operations**
3. **Use TypeScript interfaces for all props and API responses**
4. **Follow Next.js App Router conventions for file organization**
5. **Code style**: No semicolons (configured in Biome with "asNeeded")
6. **Accessibility**: Include ARIA labels, keyboard navigation, and screen reader support
7. **Focus management**: Implement automatic focus management for route changes
8. **Error recovery**: Provide retry mechanisms and graceful fallbacks

Always run `bun check` before committing and follow existing patterns in the codebase.