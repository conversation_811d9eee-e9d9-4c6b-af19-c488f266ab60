---
description: Backend API Development with Hono and Prisma
globs: ["apps/server/src/**/*.ts", "apps/server/prisma/**/*"]
alwaysApply: false
---

# API Development Standards

## Hono Router Patterns
- Use Hono router for all API endpoints
- Organize routes in `apps/server/src/routers/`
- Follow RESTful conventions where appropriate

## Request/Response Handling
```typescript
import { Hono } from 'hono';
import { z } from 'zod';

const schema = z.object({
  name: z.string(),
  email: z.string().email(),
});

app.post('/users', async (c) => {
  try {
    const body = await c.req.json();
    const validated = schema.parse(body);
    
    // Database operation
    const user = await prisma.user.create({
      data: validated,
    });
    
    return c.json({ success: true, data: user }, 201);
  } catch (error) {
    return c.json({ success: false, error: error.message }, 400);
  }
});
```

## Validation & Security
- Zod validation for all request/response data
- Proper HTTP status codes (200, 201, 400, 401, 404, 500)
- Input sanitization and validation
- No hardcoded secrets - use environment variables
- CORS configuration for frontend integration

## Database Operations
- Use Prisma for all database interactions
- Proper error handling for database operations
- Type-safe queries with Prisma client
- Use transactions for multi-step operations
- Connection pooling in production

## Error Handling
```typescript
// Consistent error response format
return c.json({
  success: false,
  error: 'Descriptive error message',
  code: 'ERROR_CODE'
}, statusCode);
```

## Environment Configuration
- Separate `.env` files for different environments
- Validate environment variables at startup
- Never commit `.env` files to version control

## Current API State (January 8, 2025)
- **Server**: Hono 4.8.10 with CORS and logging middleware configured, error-free development server
- **Health Check**: GET / endpoint returning server status implemented and tested
- **Database**: Prisma 6.13.0 configured for MongoDB with ESM support and type generation working
- **Validation**: Zod schemas ready for request/response validation with established patterns
- **Structure**: Type-safe API structure ready for expansion in `apps/server/src/routers/`
- **Schema**: Database schema empty but ready for model definitions (Prisma client generation working)
- **Client**: Frontend API client with comprehensive error handling, retry logic, and network error detection implemented
- **Error Handling**: Consistent error response patterns established with proper HTTP status codes
- **Environment**: Development environment fully configured with proper CORS for frontend integration

## AI Agent Server Testing
**Important**: Never start development servers with executeBash as these hang execution.

**Safe Testing Pattern:**
```bash
# Test endpoints (terminates quickly)
curl -s http://localhost:3000/
curl -s -X POST http://localhost:3000/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"testpass123","name":"Test User"}'
```

Follow the established patterns in `apps/server/src/routes/` for consistency.