# VSME Guru SaaS Platform Rules for Windsurf

## Project Overview
**VSME Guru** is a sustainability reporting platform for the SME market, conformant to the VSME EU standard and NSRS. Modern full-stack TypeScript application with production-ready error handling and loading states system. Complete UI foundation with responsive sidebar navigation, comprehensive accessibility features (WCAG 2.1 AA compliance), and theme system. Mock authentication system for development with Clerk integration planned.

## Technology Stack
- **Runtime**: Bun v1.2.19+ (primary), Node.js (compatible) 
- **Frontend**: Next.js 15.3.0, React 19, TailwindCSS 4.1.11, shadcn/ui, Radix UI
- **Backend**: Hono 4.8.10, Prisma 6.13.0, MongoDB, Zod validation
- **Quality**: Biome linter/formatter, Ultracite config, TypeScript strict mode
- **State Management**: TanStack Query, TanStack Form
- **Build System**: Turborepo monorepo with caching

## Essential Commands
```bash
bun dev              # Start all applications
bun dev:web          # Frontend only (port 3001)
bun dev:server       # Backend only (port 3000)
bun check            # Biome lint and format
bun check-types      # TypeScript type checking
bun build            # Build all applications
bun db:push          # Push Prisma schema changes
bun db:studio        # Open Prisma Studio
bun db:generate      # Generate Prisma client
```

## Code Quality Standards

### TypeScript Rules
- Strict mode enforced, zero tolerance for `any` types
- Use `import type` for type imports, `export type` for type exports
- No TypeScript enums, namespaces, or non-null assertions (`!`)
- Prefer `T[]` over `Array<T>`, use `as const` for literal types
- All functions must have proper return type annotations

### React & Next.js Best Practices
- Functional components with hooks only - no class components
- Never define components inside other components
- Proper dependency arrays in useEffect and other hooks
- Always include keys in mapped JSX elements
- Use `<>...</>` instead of `<React.Fragment>`
- Next.js Image component for all images - never use `<img>` tags
- Follow App Router patterns in `apps/web/src/app/`

### Accessibility Requirements (Non-negotiable)
- Use semantic HTML elements wherever possible
- Include proper ARIA attributes when needed
- No positive tabIndex values - use 0 or -1 only
- Always include `type` attribute on button elements
- Pair onClick handlers with onKeyUp/onKeyDown/onKeyPress
- Pair onMouseOver/onMouseOut with onFocus/onBlur
- Ensure screen reader compatibility and keyboard navigation

### File Organization & Naming
```
apps/
├── server/          # Hono API routes in src/routers/
└── web/             # Next.js components in src/components/
```

**Naming Conventions:**
- Files: `kebab-case` (user-profile.tsx)
- Components: `PascalCase` (UserProfile)  
- Functions: `camelCase` (getUserProfile)
- Constants: `SCREAMING_SNAKE_CASE` (API_BASE_URL)
- Types/Interfaces: `PascalCase` (UserProfile, ApiResponse)

### Import Organization (Strict Order)
1. External libraries (React, Next.js, Hono, etc.)
2. Internal utilities and components
3. Relative imports (./components, ../utils)
4. Type-only imports (always use `import type`)

### Database & API Development
- Use Prisma for ALL database operations with proper error handling
- Hono routers for all API endpoints with consistent patterns
- Zod validation for all request/response data
- Proper HTTP status codes (200, 201, 400, 401, 404, 500)
- Consistent error response format across all endpoints

### Security & Environment
- Never hardcode secrets, API keys, or sensitive data
- Use environment variables for all configuration
- Input validation with Zod for all user inputs
- Never use console.log in production code
- HTTPS in production, proper CORS configuration

### Performance Guidelines
- Use Next.js Image component with proper sizing
- Implement loading states for all async operations
- React.memo for expensive components that re-render frequently
- Dynamic imports for code splitting large components
- Database indexes for frequently queried fields
- Connection pooling for database connections

### Error Handling Pattern (Required)
```typescript
// Component error boundaries (always required)
import { ErrorBoundary } from '@/components/common/error-boundary'

<ErrorBoundary>
  <YourComponent />
</ErrorBoundary>

// API calls with error handling
const { data, error, isLoading, execute } = useApiCall(apiFunction)
if (isLoading) return <InlineLoader />
if (error) return <ErrorDisplay error={error} onRetry={execute} />

// Server-side error handling
try {
  const result = await operation();
  return { success: true, data: result };
} catch (error) {
  console.error('Operation failed:', error);
  return { success: false, error: error.message };
}
```

### Loading State Pattern (Required)
```typescript
import { InlineLoader, PageLoader, ButtonLoader, DashboardLoading } from '@/components/common/loading'

// Button loading state
<Button disabled={isLoading}>
  {isLoading ? <ButtonLoader className="mr-2" /> : null}
  {isLoading ? 'Submitting...' : 'Submit'}
</Button>

// Page-level loading
if (isLoading) return <PageLoader text="Loading..." />
```

### Accessibility Pattern (Required)
```typescript
// Navigation with proper ARIA roles
<SidebarMenu role="navigation" aria-label="Main navigation">
  <Link 
    href={item.url}
    aria-expanded={item.isActive}
    aria-describedby={item.items?.length ? `${item.title}-submenu` : undefined}
  >
    {item.icon && <item.icon aria-hidden="true" />}
    <span>{item.title}</span>
  </Link>
</SidebarMenu>

// User menu with descriptive labels
<SidebarMenuButton aria-label={`User menu for ${user.name}`}>
  <Avatar>
    <AvatarImage alt={`${user.name}'s profile picture`} src={user.avatar} />
    <AvatarFallback aria-label={`${user.name} initials`}>
      {initials}
    </AvatarFallback>
  </Avatar>
</SidebarMenuButton>

// Focus management for route changes
const useFocusManagement = () => {
  const pathname = usePathname()
  
  useEffect(() => {
    const mainContent = document.querySelector('main')
    if (mainContent) {
      mainContent.setAttribute('tabindex', '-1')
      mainContent.focus()
      setTimeout(() => mainContent.removeAttribute('tabindex'), 100)
    }
  }, [pathname])
}
```

## Critical Development Rules
- Always run `bun check` before committing any code
- Examine existing patterns in components and routers before creating new ones  
- Follow established TypeScript configurations strictly
- Use test-driven development approach when tests exist
- Leverage Turborepo caching for optimal build performance
- Never commit .env files - always use .env.example templates

## Environment Setup Requirements
- MongoDB instance required (local or cloud)
- Copy .env.example to .env in both apps/web and apps/server
- Use `bun install` for dependency management
- Ensure all environment variables are properly configured

## Current Implementation Status (January 8, 2025)

### Major Accomplishments Completed
- [x] **Backend API foundation** with Hono server structure, health check route, and error-free development server
- [x] **Database and validation setup** with Prisma 6.13.0 and Zod (MongoDB configured, schema ready for models)
- [x] **Mock authentication context** for UI development with localStorage persistence (Clerk integration documented)
- [x] **Complete sidebar-07 block implementation** with collapsible navigation and keyboard accessibility
- [x] **Comprehensive error handling system** (class-based error boundaries, global pages, recovery, retry logic)
- [x] **Loading states system** (skeleton, inline, page, button loaders with accessibility support)
- [x] **Marketing page** with VSME Guru branding, Norwegian content, and responsive design
- [x] **Dashboard layout** with responsive sidebar, breadcrumb navigation, and focus management
- [x] **Complete shadcn/ui component integration** with 20+ components and accessibility features
- [x] **API client** with comprehensive error handling, retry logic, network error detection, and type safety
- [x] **Accessibility implementation** (WCAG 2.1 AA compliance, ARIA labels, keyboard navigation, screen reader support)
- [x] **Focus management system** for route changes and keyboard navigation with automatic focus restoration
- [x] **Theme system** with dark/light mode, system preference detection, and smooth transitions
- [x] **Code quality system** with Biome + Ultracite (no semicolons preference, comprehensive TypeScript coverage)

### Ready for Implementation
- [ ] Real authentication with Clerk integration (spec documented)
- [ ] Database connection and first models (Prisma ready)
- [ ] API routes expansion (Hono structure ready)
- [ ] Testing framework setup (Vitest, Testing Library, Playwright planned)

### Current State (Production-Ready Foundation)
- **Frontend**: Complete layout system with comprehensive error handling, loading states, responsive design, and theme system
- **Backend**: Health check endpoint, Hono 4.8.10 with CORS and logging middleware, Prisma configured, ready for route expansion
- **Database**: MongoDB configured with Prisma 6.13.0 and ESM support, schema ready for model definitions, type generation working
- **UI Components**: Complete shadcn/ui integration with 20+ components, accessibility features, and theme support
- **Error Handling**: Production-ready class-based error boundaries, global error pages, recovery mechanisms, and retry logic
- **API Integration**: Type-safe client with comprehensive error handling, retry logic, and network error detection
- **Authentication**: Mock authentication context with localStorage persistence for development (Clerk integration documented)
- **Accessibility**: WCAG 2.1 AA compliance, full ARIA support, keyboard navigation, screen reader compatibility, automatic focus management
- **Code Quality**: Biome + Ultracite with no semicolons preference, comprehensive TypeScript coverage, error-free runtime
- **Testing**: No testing framework implemented yet (Vitest, Testing Library, Playwright planned with comprehensive strategy documented)

## AI Agent Guidelines

### Server Testing (Critical)
**Never start development servers** - they hang execution. Instead:
1. Ask user to start servers in their terminal
2. Use `curl` commands for API testing (these terminate quickly)
3. Request user feedback on terminal output and errors

### shadcn/ui Integration Rules
1. Use `list_components()` and `list_blocks()` to discover available assets
2. Prioritize blocks for complex patterns (login pages, dashboards, calendars)
3. Always call `get_component_demo()` before implementing components
4. Use `get_component()` for single components, `get_block()` for composite blocks

### Documentation Evolution
- Update steering documents **after** implementing features, not before
- Use real code examples from actual implementation
- Check current state documents before starting new features

## Key Implementation Insights (From Real Development Experience)
1. **Error Boundaries Are Critical**: Implementing comprehensive error handling early prevents cascading failures and improves debugging significantly
2. **Loading States Matter**: Consistent loading experience significantly improves perceived performance and user confidence
3. **Code Quality Tools**: Biome + Ultracite combination provides excellent developer experience with subsecond performance
4. **Documentation Evolution**: Update docs after implementation, not before - use real code examples from actual implementation
5. **Accessibility First**: WCAG 2.1 AA compliance, ARIA labels, keyboard navigation, and screen reader support implemented from the start
6. **Focus Management**: Automatic focus management on route changes improves accessibility and UX significantly
7. **Semicolon Preference**: Team preference for no semicolons successfully implemented with "asNeeded" configuration
8. **Type Safety First**: TypeScript-first approach prevented many runtime issues and improved developer confidence
9. **Reactive Documentation**: Documentation that evolves with implementation is more accurate and useful than predictive documentation

## Established Best Practices (From Real Implementation)
1. **Always wrap components with error boundaries**
2. **Implement loading states for all async operations**
3. **Use TypeScript interfaces for all props and API responses**
4. **Follow Next.js App Router conventions for file organization**
5. **Maintain comprehensive documentation for complex implementations**
6. **Code style**: No semicolons (configured in Biome with "asNeeded")
7. **Accessibility**: Include ARIA labels, keyboard navigation, and screen reader support
8. **Focus management**: Implement automatic focus management for route changes
9. **Error recovery**: Provide retry mechanisms and graceful fallbacks

Focus on type safety, accessibility compliance, and following established architectural patterns. Always examine existing code structure before implementing new features to maintain consistency across the codebase.