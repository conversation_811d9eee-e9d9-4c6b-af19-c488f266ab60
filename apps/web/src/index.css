@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:where(.dark, .dark *));

@theme {
  --font-sans:
    "<PERSON>", "<PERSON><PERSON><PERSON>", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji",
    "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

html,
body {
  @apply bg-white dark:bg-gray-950;

  @media (prefers-color-scheme: dark) {
    color-scheme: dark;
  }
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  /* Primary indigo colors */
  --primary: oklch(0.57 0.23 276);
  /* Indigo-600 equivalent, close to #4f46e5 */
  --primary-foreground: oklch(0.985 0 0);
  /* Secondary emerald/teal colors */
  --secondary: oklch(0.961 0.032 166.468);
  /* Emerald-50 equivalent */
  --secondary-foreground: oklch(0.145 0.118 166.468);
  /* Emerald-900 equivalent */
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  /* Accent using emerald */
  --accent: oklch(0.961 0.032 166.468);
  /* Emerald-50 equivalent */
  --accent-foreground: oklch(0.145 0.118 166.468);
  /* Emerald-900 equivalent */
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.57 0.23 276);
  /* Indigo-600 for focus rings */
  /* Chart colors with indigo and emerald theme */
  --chart-1: oklch(0.57 0.23 276);
  /* Indigo-600 */
  --chart-2: oklch(0.646 0.118 166.468);
  /* Emerald-600 */
  --chart-3: oklch(0.42 0.23 276);
  /* Indigo-800 */
  --chart-4: oklch(0.828 0.118 166.468);
  /* Emerald-400 */
  --chart-5: oklch(0.72 0.23 276);
  /* Indigo-400 */
  /* Sidebar with indigo primary theme */
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.57 0.23 276);
  /* Indigo-600 */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.961 0.032 166.468);
  /* Emerald-50 */
  --sidebar-accent-foreground: oklch(0.145 0.118 166.468);
  /* Emerald-900 */
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.57 0.23 276);
  /* Indigo-600 */
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  /* Primary indigo colors for dark theme */
  --primary: oklch(0.72 0.23 276);
  /* Indigo-400 equivalent */
  --primary-foreground: oklch(0.145 0 0);
  /* Secondary emerald/teal colors for dark theme */
  --secondary: oklch(0.145 0.118 166.468);
  /* Emerald-900 equivalent */
  --secondary-foreground: oklch(0.961 0.032 166.468);
  /* Emerald-50 equivalent */
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  /* Accent using emerald for dark theme */
  --accent: oklch(0.145 0.118 166.468);
  /* Emerald-900 equivalent */
  --accent-foreground: oklch(0.961 0.032 166.468);
  /* Emerald-50 equivalent */
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.72 0.23 276);
  /* Indigo-400 for focus rings */
  /* Chart colors with indigo and emerald theme for dark mode */
  --chart-1: oklch(0.72 0.23 276);
  /* Indigo-400 */
  --chart-2: oklch(0.828 0.118 166.468);
  /* Emerald-400 */
  --chart-3: oklch(0.57 0.23 276);
  /* Indigo-600 */
  --chart-4: oklch(0.646 0.118 166.468);
  /* Emerald-600 */
  --chart-5: oklch(0.42 0.23 276);
  /* Indigo-800 */
  /* Sidebar with indigo primary theme for dark mode */
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.72 0.23 276);
  /* Indigo-400 */
  --sidebar-primary-foreground: oklch(0.145 0 0);
  --sidebar-accent: oklch(0.145 0.118 166.468);
  /* Emerald-900 */
  --sidebar-accent-foreground: oklch(0.961 0.032 166.468);
  /* Emerald-50 */
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.72 0.23 276);
  /* Indigo-400 */
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }

  /* Accessibility improvements */

  /* Enhanced focus indicators for keyboard navigation */
  .keyboard-navigation *:focus {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* Screen reader only content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Skip link for keyboard users */
  .skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: white;
    color: black;
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 1000;
    transition: top 0.3s;
  }

  .skip-link:focus {
    top: 6px;
  }

  /* Ensure sufficient color contrast for focus states */
  button:focus-visible,
  a:focus-visible,
  input:focus-visible,
  select:focus-visible,
  textarea:focus-visible {
    @apply outline-2 outline-offset-2 outline-primary;
  }

  /* Improve touch targets on mobile */
  @media (max-width: 768px) {

    button,
    a,
    input,
    select,
    textarea {
      min-height: 44px;
      min-width: 44px;
    }
  }
}